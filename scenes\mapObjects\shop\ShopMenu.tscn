[gd_scene load_steps=40 format=3 uid="uid://d188nhrfdrene"]

[ext_resource type="Script" uid="uid://ec58ydwdqhf8" path="res://scenes/mapObjects/shop/ShopMenu.cs" id="1_shop_menu_script"]
[ext_resource type="Texture2D" uid="uid://d3wb8u348lrx5" path="res://resources/solaria/UI/inventory/inventory_menu_bg.png" id="2_kj0gu"]
[ext_resource type="PackedScene" uid="uid://c1jafpqv5feno" path="res://scenes/UI/inventory/MenuSelectionSector.tscn" id="3_qv7vs"]
[ext_resource type="Texture2D" uid="uid://dmbeircvebfmo" path="res://resources/solaria/UI/inventory/inventory_menu_equipments_tools.png" id="5_hrs4d"]
[ext_resource type="Texture2D" uid="uid://cdxye6tum1anb" path="res://resources/solaria/UI/inventory/close_button.png" id="6_5r3ks"]
[ext_resource type="Texture2D" uid="uid://clpqdxl0rnm1e" path="res://resources/solaria/UI/build/build_panel_select_amount.png" id="6_ukxxt"]
[ext_resource type="Texture2D" uid="uid://bgpd0wfpx3kvj" path="res://resources/solaria/UI/inventory/inventory_item_single_slot.png" id="7_ki6aw"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="8_33gkq"]
[ext_resource type="Texture2D" uid="uid://dl8bp5u75xxvx" path="res://resources/solaria/UI/build/craft_amount.png" id="9_m6iug"]
[ext_resource type="Texture2D" uid="uid://b4qihgrhky4rk" path="res://resources/solaria/UI/build/button1.png" id="10_ggicu"]
[ext_resource type="Texture2D" uid="uid://do758c0ekhhaj" path="res://resources/solaria/resources/resource_berries.png" id="10_ukxxt"]
[ext_resource type="Texture2D" uid="uid://dvv01dmhem1hh" path="res://resources/solaria/UI/build/button2.png" id="11_5ie37"]
[ext_resource type="Texture2D" uid="uid://dulxk4qo8hng" path="res://resources/solaria/resources/coin_resource.png" id="11_ki6aw"]

[sub_resource type="Animation" id="Animation_iw7eh"]
resource_name = "Close"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.03, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.05, 1.05), Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.09),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_r0oy0"]
resource_name = "Open"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.06, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.95, 0.95), Vector2(1.05, 1.05), Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_cna2i"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_v0ypl"]
_data = {
&"Close": SubResource("Animation_iw7eh"),
&"Open": SubResource("Animation_r0oy0"),
&"RESET": SubResource("Animation_cna2i")
}

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_2aw2t"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_pn6nb"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_6tk1r"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_5rpnh"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_w8di4"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_8l2fc"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_e4y6d"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_35m43"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_76lid"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_vp2yo"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_4l15q"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_iw7eh"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_cna2i"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_v0ypl"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_0py87"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_6yq8u"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_y4sqg"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_1gex7"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_onvy6"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_3eglp"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_fj7qj"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_yd80n"]

[node name="ShopMenu" type="CanvasLayer"]
script = ExtResource("1_shop_menu_script")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_v0ypl")
}

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Sprite2D" parent="Control"]
position = Vector2(-93, 0)
scale = Vector2(0.95, 0.95)
texture = ExtResource("2_kj0gu")

[node name="MenuSelectionSector1" parent="Control/Panel" instance=ExtResource("3_qv7vs")]
position = Vector2(0, -64)

[node name="MenuSelectionSector2" parent="Control/Panel" instance=ExtResource("3_qv7vs")]
position = Vector2(0, -32)

[node name="MenuSelectionSector3" parent="Control/Panel" instance=ExtResource("3_qv7vs")]

[node name="MenuSelectionSector4" parent="Control/Panel" instance=ExtResource("3_qv7vs")]
position = Vector2(0, 32)

[node name="MenuSelectionSector5" parent="Control/Panel" instance=ExtResource("3_qv7vs")]
position = Vector2(0, 64)

[node name="InfoBoard" type="Sprite2D" parent="Control/Panel"]
position = Vector2(139.947, -13)
scale = Vector2(1.05263, 1.05263)
texture = ExtResource("6_ukxxt")

[node name="ItemBackground" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(0.999268, -51.0135)
texture = ExtResource("7_ki6aw")

[node name="InfoLabel" parent="Control/Panel/InfoBoard" instance=ExtResource("8_33gkq")]
offset_left = -52.0006
offset_top = -28.0135
offset_right = 91.9994
offset_bottom = -12.0135
scale = Vector2(0.73, 0.73)
text = "SELECT_AMOUNT"

[node name="ItemFront" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(0.999268, -51.0135)

[node name="SelectAmountBg" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(-1.00085, 0.986572)
scale = Vector2(1.5, 1.5)
texture = ExtResource("9_m6iug")

[node name="AmountToProduce" parent="Control/Panel/InfoBoard" instance=ExtResource("8_33gkq")]
offset_left = -15.0006
offset_top = -13.0135
offset_right = 22.9994
offset_bottom = 20.9865
scale = Vector2(0.73, 0.73)
text = "1"

[node name="Amount1" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(-37.0006, 35.9866)
scale = Vector2(0.5, 0.5)
texture = ExtResource("10_ggicu")

[node name="AmountToProduce" parent="Control/Panel/InfoBoard/Amount1" instance=ExtResource("8_33gkq")]
offset_left = -14.0
offset_top = -14.3334
offset_right = 24.0
offset_bottom = 19.6666
scale = Vector2(0.73, 0.73)
text = "x1"

[node name="Amount25" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(-12.0004, 35.9865)
scale = Vector2(0.5, 0.5)
texture = ExtResource("10_ggicu")

[node name="AmountToProduce" parent="Control/Panel/InfoBoard/Amount25" instance=ExtResource("8_33gkq")]
offset_left = -14.0
offset_top = -14.3334
offset_right = 24.0
offset_bottom = 19.6666
scale = Vector2(0.73, 0.73)
text = "25%"

[node name="Amount50" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(12.9996, 35.9865)
scale = Vector2(0.5, 0.5)
texture = ExtResource("10_ggicu")

[node name="AmountToProduce" parent="Control/Panel/InfoBoard/Amount50" instance=ExtResource("8_33gkq")]
offset_left = -14.0
offset_top = -14.3334
offset_right = 24.0
offset_bottom = 19.6666
scale = Vector2(0.73, 0.73)
text = "50%"

[node name="AmountMax" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(37.9996, 35.9865)
scale = Vector2(0.5, 0.5)
texture = ExtResource("10_ggicu")

[node name="AmountToProduce" parent="Control/Panel/InfoBoard/AmountMax" instance=ExtResource("8_33gkq")]
offset_left = -14.0
offset_top = -14.3334
offset_right = 24.0
offset_bottom = 19.6666
scale = Vector2(0.73, 0.73)
text = "MAX"

[node name="SellButton" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(-1.00061, 56.9865)
scale = Vector2(0.59, 0.59)
texture = ExtResource("11_5ie37")

[node name="SellText" parent="Control/Panel/InfoBoard/SellButton" instance=ExtResource("8_33gkq")]
anchors_preset = -1
anchor_left = -0.0233037
anchor_top = -0.335805
anchor_right = 0.757946
anchor_bottom = -0.335805
offset_left = -30.6256
offset_top = -7.28207
offset_right = 7.37443
offset_bottom = 26.7179
scale = Vector2(0.73, 0.73)
text = "SELL_TEXT"
metadata/_edit_use_anchors_ = true

[node name="SellPrice" parent="Control/Panel/InfoBoard/SellButton" instance=ExtResource("8_33gkq")]
anchors_preset = -1
anchor_left = -0.0262353
anchor_top = -0.848705
anchor_right = 0.176889
anchor_bottom = -0.848705
offset_left = -27.4379
offset_top = 17.1307
offset_right = 10.5621
offset_bottom = 51.1307
scale = Vector2(0.73, 0.73)
text = "100"
horizontal_alignment = 2
metadata/_edit_use_anchors_ = true

[node name="CoinIcon" type="Sprite2D" parent="Control/Panel/InfoBoard/SellButton"]
position = Vector2(14.6623, 4.85351)
texture = ExtResource("11_ki6aw")

[node name="ButtonMinusOne" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = -21.0006
offset_top = 8.98651
offset_right = -7.00061
offset_bottom = 22.9865
theme_override_styles/focus = SubResource("StyleBoxEmpty_2aw2t")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_pn6nb")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_6tk1r")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_5rpnh")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_w8di4")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_8l2fc")
theme_override_styles/hover = SubResource("StyleBoxEmpty_e4y6d")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_35m43")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_76lid")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_vp2yo")
theme_override_styles/normal = SubResource("StyleBoxEmpty_4l15q")

[node name="ButtonPlusOne" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = 4.99915
offset_top = 8.98657
offset_right = 18.9991
offset_bottom = 22.9866
theme_override_styles/focus = SubResource("StyleBoxEmpty_2aw2t")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_pn6nb")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_6tk1r")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_5rpnh")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_w8di4")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_8l2fc")
theme_override_styles/hover = SubResource("StyleBoxEmpty_e4y6d")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_35m43")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_76lid")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_vp2yo")
theme_override_styles/normal = SubResource("StyleBoxEmpty_4l15q")

[node name="ButtonSetOne" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = -49.0
offset_top = 28.0
offset_right = -25.0
offset_bottom = 44.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_2aw2t")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_pn6nb")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_6tk1r")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_5rpnh")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_w8di4")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_8l2fc")
theme_override_styles/hover = SubResource("StyleBoxEmpty_e4y6d")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_35m43")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_76lid")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_vp2yo")
theme_override_styles/normal = SubResource("StyleBoxEmpty_4l15q")

[node name="ButtonSet25Percent" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = -24.0
offset_top = 28.0
offset_bottom = 44.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_2aw2t")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_pn6nb")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_6tk1r")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_5rpnh")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_w8di4")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_8l2fc")
theme_override_styles/hover = SubResource("StyleBoxEmpty_e4y6d")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_35m43")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_76lid")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_vp2yo")
theme_override_styles/normal = SubResource("StyleBoxEmpty_4l15q")

[node name="ButtonSet50Percent" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = 1.0
offset_top = 28.0
offset_right = 25.0
offset_bottom = 44.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_2aw2t")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_pn6nb")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_6tk1r")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_5rpnh")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_w8di4")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_8l2fc")
theme_override_styles/hover = SubResource("StyleBoxEmpty_e4y6d")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_35m43")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_76lid")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_vp2yo")
theme_override_styles/normal = SubResource("StyleBoxEmpty_4l15q")

[node name="ButtonSetMax" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = 26.0
offset_top = 28.0
offset_right = 50.0
offset_bottom = 44.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_2aw2t")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_pn6nb")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_6tk1r")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_5rpnh")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_w8di4")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_8l2fc")
theme_override_styles/hover = SubResource("StyleBoxEmpty_e4y6d")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_35m43")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_76lid")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_vp2yo")
theme_override_styles/normal = SubResource("StyleBoxEmpty_4l15q")

[node name="ButtonSell" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = -20.0
offset_top = 47.0
offset_right = 18.0
offset_bottom = 66.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_2aw2t")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_pn6nb")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_6tk1r")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_5rpnh")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_w8di4")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_8l2fc")
theme_override_styles/hover = SubResource("StyleBoxEmpty_e4y6d")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_35m43")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_76lid")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_vp2yo")
theme_override_styles/normal = SubResource("StyleBoxEmpty_4l15q")

[node name="ShopMenu" type="Sprite2D" parent="Control/Panel"]
position = Vector2(245.947, -5)
scale = Vector2(0.95, 0.95)
texture = ExtResource("5_hrs4d")

[node name="Item1" type="Node2D" parent="Control/Panel/ShopMenu"]
position = Vector2(3.425, 0)

[node name="ItemBackground" type="Sprite2D" parent="Control/Panel/ShopMenu/Item1"]
position = Vector2(-24.8949, -34.5263)
texture = ExtResource("7_ki6aw")

[node name="ItemForeground" type="Sprite2D" parent="Control/Panel/ShopMenu/Item1"]
position = Vector2(-25.008, -37.895)
texture = ExtResource("10_ukxxt")

[node name="Coin" type="Sprite2D" parent="Control/Panel/ShopMenu/Item1"]
position = Vector2(-15.734, -12.6316)
texture = ExtResource("11_ki6aw")

[node name="AvailableAmount" parent="Control/Panel/ShopMenu/Item1" instance=ExtResource("8_33gkq")]
offset_left = -36.0
offset_top = -31.0
offset_right = 1.0
offset_bottom = -14.0
scale = Vector2(0.605, 0.605)
text = "x1"
horizontal_alignment = 2

[node name="Price" parent="Control/Panel/ShopMenu/Item1" instance=ExtResource("8_33gkq")]
offset_left = -43.1024
offset_top = -18.9474
offset_right = -13.1024
offset_bottom = -2.94737
scale = Vector2(0.73, 0.73)
text = "600"
horizontal_alignment = 2

[node name="ItemButton" type="Button" parent="Control/Panel/ShopMenu/Item1"]
offset_left = -35.0
offset_top = -45.0
offset_right = -15.0
offset_bottom = -5.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_2aw2t")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_pn6nb")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_6tk1r")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_5rpnh")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_w8di4")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_8l2fc")
theme_override_styles/hover = SubResource("StyleBoxEmpty_e4y6d")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_35m43")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_76lid")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_vp2yo")
theme_override_styles/normal = SubResource("StyleBoxEmpty_4l15q")

[node name="Item2" type="Node2D" parent="Control/Panel/ShopMenu"]
position = Vector2(44.79, 0)

[node name="ItemBackground" type="Sprite2D" parent="Control/Panel/ShopMenu/Item2"]
position = Vector2(-24.8949, -34.5263)
texture = ExtResource("7_ki6aw")

[node name="ItemForeground" type="Sprite2D" parent="Control/Panel/ShopMenu/Item2"]
position = Vector2(-25.008, -37.895)
texture = ExtResource("10_ukxxt")

[node name="Coin" type="Sprite2D" parent="Control/Panel/ShopMenu/Item2"]
position = Vector2(-15.734, -12.6316)
texture = ExtResource("11_ki6aw")

[node name="AvailableAmount" parent="Control/Panel/ShopMenu/Item2" instance=ExtResource("8_33gkq")]
offset_left = -36.0
offset_top = -31.0
offset_right = 1.0
offset_bottom = -12.0
scale = Vector2(0.605, 0.605)
text = "x1"
horizontal_alignment = 2

[node name="Price" parent="Control/Panel/ShopMenu/Item2" instance=ExtResource("8_33gkq")]
offset_left = -43.1024
offset_top = -18.9474
offset_right = -13.1024
offset_bottom = -2.94737
scale = Vector2(0.73, 0.73)
text = "600"
horizontal_alignment = 2

[node name="ItemButton" type="Button" parent="Control/Panel/ShopMenu/Item2"]
offset_left = -35.0
offset_top = -45.0
offset_right = -15.0
offset_bottom = -5.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_2aw2t")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_pn6nb")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_6tk1r")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_5rpnh")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_w8di4")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_8l2fc")
theme_override_styles/hover = SubResource("StyleBoxEmpty_e4y6d")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_35m43")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_76lid")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_vp2yo")
theme_override_styles/normal = SubResource("StyleBoxEmpty_4l15q")

[node name="Item3" type="Node2D" parent="Control/Panel/ShopMenu"]
position = Vector2(3.425, 54.12)

[node name="ItemBackground" type="Sprite2D" parent="Control/Panel/ShopMenu/Item3"]
position = Vector2(-24.8949, -34.5263)
texture = ExtResource("7_ki6aw")

[node name="ItemForeground" type="Sprite2D" parent="Control/Panel/ShopMenu/Item3"]
position = Vector2(-25.008, -37.895)
texture = ExtResource("10_ukxxt")

[node name="Coin" type="Sprite2D" parent="Control/Panel/ShopMenu/Item3"]
position = Vector2(-15.734, -12.6316)
texture = ExtResource("11_ki6aw")

[node name="AvailableAmount" parent="Control/Panel/ShopMenu/Item3" instance=ExtResource("8_33gkq")]
offset_left = -36.0
offset_top = -31.0
offset_right = 1.0
offset_bottom = -14.0
scale = Vector2(0.605, 0.605)
text = "x1"
horizontal_alignment = 2

[node name="Price" parent="Control/Panel/ShopMenu/Item3" instance=ExtResource("8_33gkq")]
offset_left = -43.1024
offset_top = -18.9474
offset_right = -13.1024
offset_bottom = -2.94737
scale = Vector2(0.73, 0.73)
text = "600"
horizontal_alignment = 2

[node name="ItemButton" type="Button" parent="Control/Panel/ShopMenu/Item3"]
offset_left = -35.0
offset_top = -45.0
offset_right = -15.0
offset_bottom = -5.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_2aw2t")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_pn6nb")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_6tk1r")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_5rpnh")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_w8di4")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_8l2fc")
theme_override_styles/hover = SubResource("StyleBoxEmpty_e4y6d")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_35m43")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_76lid")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_vp2yo")
theme_override_styles/normal = SubResource("StyleBoxEmpty_4l15q")

[node name="Item4" type="Node2D" parent="Control/Panel/ShopMenu"]
position = Vector2(44.79, 54.12)

[node name="ItemBackground" type="Sprite2D" parent="Control/Panel/ShopMenu/Item4"]
position = Vector2(-24.8949, -34.5263)
texture = ExtResource("7_ki6aw")

[node name="ItemForeground" type="Sprite2D" parent="Control/Panel/ShopMenu/Item4"]
position = Vector2(-25.008, -37.895)
texture = ExtResource("10_ukxxt")

[node name="Coin" type="Sprite2D" parent="Control/Panel/ShopMenu/Item4"]
position = Vector2(-15.734, -12.6316)
texture = ExtResource("11_ki6aw")

[node name="AvailableAmount" parent="Control/Panel/ShopMenu/Item4" instance=ExtResource("8_33gkq")]
offset_left = -36.0
offset_top = -31.0
offset_right = 1.0
offset_bottom = -14.0
scale = Vector2(0.605, 0.605)
text = "x1"
horizontal_alignment = 2

[node name="Price" parent="Control/Panel/ShopMenu/Item4" instance=ExtResource("8_33gkq")]
offset_left = -43.1024
offset_top = -18.9474
offset_right = -13.1024
offset_bottom = -2.94737
scale = Vector2(0.73, 0.73)
text = "600"
horizontal_alignment = 2

[node name="ItemButton" type="Button" parent="Control/Panel/ShopMenu/Item4"]
offset_left = -35.0
offset_top = -45.0
offset_right = -15.0
offset_bottom = -5.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_2aw2t")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_pn6nb")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_6tk1r")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_5rpnh")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_w8di4")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_8l2fc")
theme_override_styles/hover = SubResource("StyleBoxEmpty_e4y6d")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_35m43")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_76lid")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_vp2yo")
theme_override_styles/normal = SubResource("StyleBoxEmpty_4l15q")

[node name="NewOfferLabel" parent="Control/Panel/ShopMenu" instance=ExtResource("8_33gkq")]
offset_left = -45.0
offset_top = 63.0
offset_right = 102.0
offset_bottom = 98.0
scale = Vector2(0.615, 0.615)
text = "TEXT_NEW_OFFER_EVERY_DAY"

[node name="ShopLabel" parent="Control/Panel/ShopMenu" instance=ExtResource("8_33gkq")]
offset_left = -44.0
offset_top = -87.0
offset_right = 72.0
offset_bottom = -58.0
scale = Vector2(0.77, 0.77)
text = "SHOP_TEXT"

[node name="CloseButtonSprite" type="Sprite2D" parent="Control/Panel"]
position = Vector2(293.947, -88)
texture = ExtResource("6_5r3ks")

[node name="CloseButton" type="Button" parent="Control/Panel"]
offset_left = 283.947
offset_top = -100.0
offset_right = 303.947
offset_bottom = -78.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_iw7eh")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_cna2i")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_v0ypl")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_0py87")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_6yq8u")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_y4sqg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_1gex7")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_onvy6")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_3eglp")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_fj7qj")
theme_override_styles/normal = SubResource("StyleBoxEmpty_yd80n")
