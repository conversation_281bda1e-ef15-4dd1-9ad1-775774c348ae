using Godot;
using System;
using System.Collections.Generic;

public partial class Workbench : Node2D, IDestroyableObject
{
	[Export] public ObjectType BuildingType { get; set; } = ObjectType.Workbench;
	[Export] public int MaxHealth { get; set; } = 20;

	private const int BUILDING_WIDTH = 2;
	private const int BUILDING_HEIGHT = 1;
	private const int TILE_SIZE = 16;

	private Sprite2D _workbenchSprite;
	private Sprite2D _upgradingToolSprite;
	private Area2D _playerDetector;
	private WorkbenchMenu _workbenchMenu;
	private Vector2I _topLeftTilePosition;
	private CustomDataLayerManager _customDataManager;
	private bool _isPlaced = false;
	private string _saveId;
	private int _currentHealth;
	private Tween _hitTween;
	private bool _isBeingDestroyed = false;
	private ProgressBar _hpBar;
	private ProgressBarVertical _upgradingProgressBar;

	private ToolType _selectedUpgradingTool = ToolType.None;
	private int _upgradingProgress = 0;
	private bool _isPlayerInRange = false;

	// Tool upgrade requirements: level -> (wooden beams, bars needed)
	private static readonly Dictionary<int, (int beams, int bars)> UpgradeRequirements = new()
	{
		{ 2, (10, 20) }, // Stone level: 20 stone + 20 stone2 (handled separately)
		{ 3, (10, 20) }, // Copper
		{ 4, (10, 20) }, // Iron
		{ 5, (10, 20) }, // Gold
		{ 6, (10, 20) }, // Indigosium
		{ 7, (10, 20) }, // Mithril
		{ 8, (10, 20) }, // Erithrydium
		{ 9, (10, 20) }, // Adamantite
		{ 10, (10, 20) } // Uranium
	};

	// Map upgrade levels to required bar types
	private static readonly Dictionary<int, ResourceType> LevelToBarType = new()
	{
		{ 3, ResourceType.CopperBar },
		{ 4, ResourceType.IronBar },
		{ 5, ResourceType.GoldBar },
		{ 6, ResourceType.IndigosiumBar },
		{ 7, ResourceType.MithrilBar },
		{ 8, ResourceType.ErithrydiumBar },
		{ 9, ResourceType.AdamantiteBar },
		{ 10, ResourceType.UraniumBar }
	};

	// Upgrade time requirements (in hammer hits)
	private static readonly Dictionary<int, int> UpgradeTimeRequired = new()
	{
		{ 2, 10 }, // Stone
		{ 3, 15 }, // Copper
		{ 4, 20 }, // Iron
		{ 5, 25 }, // Gold
		{ 6, 30 }, // Indigosium
		{ 7, 35 }, // Mithril
		{ 8, 40 }, // Erithrydium
		{ 9, 45 }, // Adamantite
		{ 10, 50 } // Uranium
	};

	private readonly Color _normalColor = new Color(1.0f, 1.0f, 1.0f, 1.0f);
	private readonly Color _invalidColor = new Color(1.0f, 0.5f, 0.5f, 0.8f);
	private readonly Color _hitColor = new Color(1.0f, 0.42f, 0.27f, 1.0f); // ff6c44 color
	private readonly Color _transparentColor = new Color(1.0f, 1.0f, 1.0f, 0.5f);
	private readonly float _hitAnimationDuration = 0.3f;
	private readonly float _hitTintStrength = 0.5f;

	private bool _isPlayerBehind = false;

	public override void _Ready()
	{
		_currentHealth = MaxHealth;

		_workbenchSprite = GetNode<Sprite2D>("WorkbenchSprite");
		if (_workbenchSprite == null)
		{
			GD.PrintErr("Workbench: WorkbenchSprite node not found!");
			return;
		}

		_upgradingToolSprite = GetNode<Sprite2D>("UpgradingTool");
		if (_upgradingToolSprite == null)
		{
			GD.PrintErr("Workbench: UpgradingTool sprite not found!");
		}

		_playerDetector = GetNode<Area2D>("PlayerDetector");
		if (_playerDetector == null)
		{
			GD.PrintErr("Workbench: PlayerDetector not found!");
		}
		else
		{
			_playerDetector.CollisionMask = 4;
			_playerDetector.AreaEntered += OnPlayerEntered;
			_playerDetector.AreaExited += OnPlayerExited;
		}

		_hpBar = GetNode<ProgressBar>("ProgressBar");
		if (_hpBar == null)
		{
			GD.PrintErr("Workbench: ProgressBar node not found!");
		}

		_upgradingProgressBar = GetNode<ProgressBarVertical>("ProgressBarVertical");
		if (_upgradingProgressBar == null)
		{
			GD.PrintErr("Workbench: ProgressBarVertical node not found!");
		}

		if (_isPlaced)
		{
			_workbenchSprite.Modulate = _normalColor;
		}

		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");
		if (_customDataManager == null)
		{
			GD.PrintErr("Workbench: CustomDataLayerManager not found!");
		}

		// Get WorkbenchMenu from the workbench scene
		_workbenchMenu = GetNode<WorkbenchMenu>("WorkbenchMenu");
		if (_workbenchMenu != null)
		{
			_workbenchMenu.SetWorkbench(this);
		}
		else
		{
			GD.PrintErr("Workbench: WorkbenchMenu not found in workbench scene!");
		}

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed += OnPickaxeUsed;
			CommonSignals.Instance.HammerUsed += OnHammerUsed;
		}

		AddToGroup("buildings");

		UpdateHPBar();

		// Initialize upgrading display if needed (for loaded games)
		if (_selectedUpgradingTool != ToolType.None || _upgradingProgress > 0)
		{
			UpdateUpgradingToolDisplay();
		}
	}

	public override void _Process(double delta)
	{
		if (!_isPlaced || _workbenchSprite == null) return;

		UpdateTransparency();
	}

	private void UpdateTransparency()
	{
		if (_workbenchSprite == null) return;

		var player = GetNode<PlayerController>("/root/world/Player");
		if (player == null) return;

		bool playerBehind = player.GlobalPosition.Y < GlobalPosition.Y;

		float horizontalDistance = Math.Abs(player.GlobalPosition.X - GlobalPosition.X);
		bool closeEnoughHorizontally = horizontalDistance <= 16.0f;

		float verticalDistance = GlobalPosition.Y - player.GlobalPosition.Y;
		bool notTooFarAbove = verticalDistance <= 16.0f;

		var modulate = _workbenchSprite.Modulate;
		modulate.A = (playerBehind && closeEnoughHorizontally && notTooFarAbove) ? 0.5f : 1.0f;
		_workbenchSprite.Modulate = modulate;
	}

	public void SetTilePosition(Vector2I topLeftTile)
	{
		_topLeftTilePosition = topLeftTile;

		float centerX = (topLeftTile.X + 1.0f) * TILE_SIZE;
		float centerY = (topLeftTile.Y + 0.5f) * TILE_SIZE;
		GlobalPosition = new Vector2(centerX, centerY);
	}

	public Vector2I GetTopLeftTilePosition()
	{
		return _topLeftTilePosition;
	}

	public bool CanBePlacedAt(Vector2I topLeftTile)
	{
		if (_customDataManager == null) return false;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = topLeftTile + new Vector2I(x, y);
				var tileData = _customDataManager.GetTileData(tilePos);

				if (!tileData.CanBuilding || tileData.ObjectTypePlaced != ObjectTypePlaced.None)
				{
					return false;
				}
			}
		}

		return true;
	}

	public void PlaceBuilding()
	{
		if (_customDataManager == null || _isPlaced) return;

		// Consume resources for building the workbench
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager != null)
		{
			bool hasPlank = resourcesManager.RemoveResource(ResourceType.Plank, BuildMenu.WORKBENCH_PLANK_REQUIRED);
			bool hasStoneBrick = resourcesManager.RemoveResource(ResourceType.StoneBrick, BuildMenu.WORKBENCH_STONEBRICK_REQUIRED);

			if (!hasPlank || !hasStoneBrick)
			{
				GD.PrintErr("Workbench: Failed to consume resources for building!");
				return;
			}
		}

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = _topLeftTilePosition + new Vector2I(x, y);
				_customDataManager.SetObjectPlaced(tilePos, ObjectTypePlaced.Building);
			}
		}

		_isPlaced = true;
		if (_workbenchSprite != null)
		{
			_workbenchSprite.Modulate = _normalColor;
		}

		_saveId = ResourcesManager.Instance?.AddBuilding(_topLeftTilePosition, "Workbench", (int)BuildingType);

		SaveUpgradingState();

		GD.Print($"Workbench: Building placed at {_topLeftTilePosition} (consumed 10 wood, 5 stone)");
	}

	public void DestroyBuilding()
	{
		if (_customDataManager == null || !_isPlaced || _isBeingDestroyed) return;
		_isBeingDestroyed = true;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = _topLeftTilePosition + new Vector2I(x, y);
				_customDataManager.ClearObjectPlaced(tilePos);
			}
		}

		// Save the updated custom layer data immediately
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager != null)
		{
			resourcesManager.SaveCustomLayerData(_customDataManager);
		}

		if (!string.IsNullOrEmpty(_saveId))
		{
			ResourcesManager.Instance?.RemoveBuildingById(_saveId);
		}

		_hitTween?.Kill();
		_hitTween = null;
		_workbenchSprite = null;
		_customDataManager = null;
		_isPlaced = false;
		_saveId = null;

		QueueFree();
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed -= OnPickaxeUsed;
			CommonSignals.Instance.HammerUsed -= OnHammerUsed;
		}

		if (_playerDetector != null)
		{
			_playerDetector.AreaEntered -= OnPlayerEntered;
			_playerDetector.AreaExited -= OnPlayerExited;
		}
	}

	public void SetPlacementFeedback(bool canPlace)
	{
		Color targetColor = canPlace ? _normalColor : _invalidColor;
		if (_workbenchSprite != null)
		{
			_workbenchSprite.Modulate = targetColor;
		}
	}

	public bool IsPlaced()
	{
		return _isPlaced;
	}

	public void TakeDamage(int damage)
	{
		if (_isBeingDestroyed) return;

		_currentHealth -= damage;
		UpdateHPBar();
		SaveUpgradingState();
		PlayHitAnimation();

		if (_currentHealth <= 0)
		{
			DestroyBuilding();
		}
	}

	private void PlayHitAnimation()
	{
		if (_workbenchSprite == null) return;

		_hitTween?.Kill();
		_hitTween = CreateTween();
		_hitTween.SetParallel(true);

		_hitTween.TweenProperty(_workbenchSprite, "modulate", _hitColor, _hitAnimationDuration * 0.1f);
		_hitTween.TweenProperty(_workbenchSprite, "modulate", _normalColor, _hitAnimationDuration * 0.9f)
				.SetDelay(_hitAnimationDuration * 0.1f);

		var originalScale = _workbenchSprite.Scale;
		var smallerScale = originalScale * 0.9f;
		var biggerScale = originalScale * 1.1f;

		_hitTween.TweenProperty(_workbenchSprite, "scale", smallerScale, _hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_workbenchSprite, "scale", biggerScale, _hitAnimationDuration * 0.3f)
				.SetDelay(_hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_workbenchSprite, "scale", originalScale, _hitAnimationDuration * 0.4f)
				.SetDelay(_hitAnimationDuration * 0.6f);
	}

	public int GetCurrentHealth()
	{
		return _currentHealth;
	}

	private void UpdateHPBar()
	{
		if (_hpBar == null) return;

		float healthPercentage = (float)_currentHealth / MaxHealth;

		if (_currentHealth >= MaxHealth)
		{
			_hpBar.Hide();
		}
		else
		{
			_hpBar.Show();
			_hpBar.SetProgress(healthPercentage);
		}
	}

	public bool CanBeHitFrom(Vector2I playerTilePosition)
	{
		var distance = _topLeftTilePosition - playerTilePosition;
		return Math.Abs(distance.X) <= 1 && Math.Abs(distance.Y) <= 1;
	}

	public bool CanBeHitFrom(Vector2I playerTilePosition, Vector2I hitTilePosition)
	{
		var distance = hitTilePosition - playerTilePosition;
		return Math.Abs(distance.X) <= 1 && Math.Abs(distance.Y) <= 1;
	}

	public Vector2I GetTilePosition()
	{
		return _topLeftTilePosition;
	}

	public void SetCurrentHealth(int health)
	{
		_currentHealth = Math.Max(0, Math.Min(health, MaxHealth));
		UpdateHPBar();
	}

	public void Repair(int repairAmount)
	{
		if (_isBeingDestroyed) return;

		_currentHealth = Math.Min(_currentHealth + repairAmount, MaxHealth);
		PlayHitAnimation();
		UpdateHPBar();
		SaveUpgradingState();

		GD.Print($"Workbench: Repaired for {repairAmount} health. Current: {_currentHealth}/{MaxHealth}");
	}

	private void OnPickaxeUsed(Vector2I tilePosition, int damage)
	{
		if (!_isPlaced) return;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I occupiedTile = _topLeftTilePosition + new Vector2I(x, y);
				if (occupiedTile == tilePosition)
				{
					Vector2I playerTile = GetPlayerTilePosition();
					if (CanBeHitFrom(playerTile, tilePosition))
					{
						TakeDamage(damage);
					}
					return;
				}
			}
		}
	}

	private void OnHammerUsed(Vector2I tilePosition, int repairAmount)
	{
		if (!_isPlaced) return;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I occupiedTile = _topLeftTilePosition + new Vector2I(x, y);
				if (occupiedTile == tilePosition)
				{
					Vector2I playerTile = GetPlayerTilePosition();
					if (CanBeHitFrom(playerTile, tilePosition))
					{
						Repair(repairAmount);
						OnHammerUsedForUpgrading(repairAmount);
					}
					return;
				}
			}
		}
	}

	private Vector2I GetPlayerTilePosition()
	{
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null)
		{
			return new Vector2I(
				Mathf.FloorToInt(player.GlobalPosition.X / 16),
				Mathf.FloorToInt(player.GlobalPosition.Y / 16)
			);
		}
		return Vector2I.Zero;
	}

	public Vector2I[] GetOccupiedTiles()
	{
		var tiles = new Vector2I[BUILDING_WIDTH * BUILDING_HEIGHT];
		int index = 0;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				tiles[index++] = _topLeftTilePosition + new Vector2I(x, y);
			}
		}

		return tiles;
	}

	public void SetSaveId(string saveId)
	{
		_saveId = saveId;
	}

	public void SetCustomDataManager(CustomDataLayerManager customDataManager)
	{
		_customDataManager = customDataManager;
	}

	public void LoadFromSaveData(BuildingData buildingData)
	{
		_saveId = buildingData.Id;
		_topLeftTilePosition = new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY);
		BuildingType = (ObjectType)buildingData.BuildingId;

		float centerX = (_topLeftTilePosition.X + 1.0f) * TILE_SIZE;
		float centerY = (_topLeftTilePosition.Y + 0.5f) * TILE_SIZE;
		GlobalPosition = new Vector2(centerX, centerY);

		_isPlaced = true;
		if (_workbenchSprite != null)
		{
			_workbenchSprite.Modulate = _normalColor;
		}

		if (_customDataManager != null)
		{
			for (int x = 0; x < BUILDING_WIDTH; x++)
			{
				for (int y = 0; y < BUILDING_HEIGHT; y++)
				{
					Vector2I tilePos = _topLeftTilePosition + new Vector2I(x, y);
					_customDataManager.SetObjectPlaced(tilePos, ObjectTypePlaced.Building);
				}
			}
		}

		_currentHealth = buildingData.CurrentHealth;
		_selectedUpgradingTool = (ToolType)buildingData.SelectedCraftingResource; // Reuse this field
		_upgradingProgress = buildingData.CraftingProgress; // Reuse this field

		GD.Print($"Workbench: Loaded from save - Health: {_currentHealth}, Tool: {_selectedUpgradingTool}, Progress: {_upgradingProgress}");

		UpdateHPBar();

		if (_selectedUpgradingTool != ToolType.None || _upgradingProgress > 0)
		{
			CallDeferred(nameof(UpdateUpgradingToolDisplay));
		}
	}

	public override void _Input(InputEvent @event)
	{
		if (!_isPlayerInRange || !_isPlaced) return;

		if (@event is InputEventKey keyEvent && keyEvent.Pressed)
		{
			if (keyEvent.Keycode == Key.R)
			{
				OpenWorkbenchMenu();
			}
		}
	}

	private void OnPlayerEntered(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			_isPlayerInRange = true;
			GD.Print("Workbench: Player entered range - press 'R' to open upgrade menu");
		}
	}

	private void OnPlayerExited(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			_isPlayerInRange = false;
			GD.Print("Workbench: Player left range");
		}
	}

	private void OpenWorkbenchMenu()
	{
		CheckAndUpdateUpgradingSelection();

		// Set the correct workbench reference in the menu before opening
		if (MenuManager.Instance != null)
		{
			var workbenchMenu = MenuManager.Instance.GetRegisteredMenu("WorkbenchMenu") as WorkbenchMenu;
			if (workbenchMenu != null)
			{
				workbenchMenu.SetWorkbench(this);
				MenuManager.Instance.OpenMenu("WorkbenchMenu");
			}
			else
			{
				GD.PrintErr("Workbench: WorkbenchMenu not found in MenuManager!");
			}
		}
		else
		{
			// Fallback to direct opening if MenuManager is not available
			if (_workbenchMenu != null)
			{
				_workbenchMenu.SetWorkbench(this);
				_workbenchMenu.OpenMenu();
			}
			else
			{
				GD.PrintErr("Workbench: WorkbenchMenu instance not found!");
			}
		}
	}

	private void CheckAndUpdateUpgradingSelection()
	{
		if (_selectedUpgradingTool != ToolType.None && _upgradingProgress == 0)
		{
			if (!CanAffordCurrentToolUpgrade())
			{
				GD.Print($"Workbench: Auto-deselecting {_selectedUpgradingTool} - insufficient resources");
				_selectedUpgradingTool = ToolType.None;
				UpdateUpgradingToolDisplay();
				SaveUpgradingState();
			}
		}
	}

	public void StartUpgradingTool(ToolType toolType)
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		int currentLevel = resourcesManager.GetToolLevel(toolType);
		int nextLevel = currentLevel + 1;

		if (nextLevel > 10)
		{
			GD.Print($"Workbench: {toolType} is already at maximum level!");
			return;
		}

		if (!CanAffordToolUpgrade(toolType, nextLevel))
		{
			GD.Print($"Workbench: Not enough resources to upgrade {toolType} to level {nextLevel}!");
			return;
		}

		_selectedUpgradingTool = toolType;
		_upgradingProgress = 0;
		UpdateUpgradingToolDisplay();
		SaveUpgradingState();

		GD.Print($"Workbench: Started upgrading {toolType} to level {nextLevel}");
	}

	private bool CanAffordToolUpgrade(ToolType toolType, int targetLevel)
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		if (targetLevel == 2) // Stone level
		{
			return resourcesManager.HasResource(ResourceType.Stone, 20) &&
				   resourcesManager.HasResource(ResourceType.Stone2, 20);
		}
		else if (targetLevel >= 3 && targetLevel <= 10)
		{
			if (!UpgradeRequirements.TryGetValue(targetLevel, out var requirements)) return false;
			if (!LevelToBarType.TryGetValue(targetLevel, out var barType)) return false;

			return resourcesManager.HasResource(ResourceType.WoodenBeam, requirements.beams) &&
				   resourcesManager.HasResource(barType, requirements.bars);
		}

		return false;
	}

	private bool CanAffordCurrentToolUpgrade()
	{
		if (_selectedUpgradingTool == ToolType.None) return false;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		int currentLevel = resourcesManager.GetToolLevel(_selectedUpgradingTool);
		int nextLevel = currentLevel + 1;

		return CanAffordToolUpgrade(_selectedUpgradingTool, nextLevel);
	}

	private void UpdateUpgradingToolDisplay()
	{
		if (_upgradingToolSprite == null)
		{
			_upgradingToolSprite = GetNode<Sprite2D>("UpgradingTool");
			if (_upgradingToolSprite == null)
			{
				GD.PrintErr("Workbench: UpgradingTool node not found in scene!");
				return;
			}
		}

		if (_upgradingProgressBar == null)
		{
			_upgradingProgressBar = GetNode<ProgressBarVertical>("ProgressBarVertical");
			if (_upgradingProgressBar == null)
			{
				GD.PrintErr("Workbench: ProgressBarVertical node not found in scene!");
			}
		}

		if (_selectedUpgradingTool == ToolType.None)
		{
			_upgradingToolSprite.Texture = null;
			_upgradingToolSprite.Visible = false;

			if (_upgradingProgressBar != null)
			{
				_upgradingProgressBar.Hide();
			}
			GD.Print("Workbench: Hidden upgrading tool display (no tool selected)");
		}
		else
		{
			var textureManager = TextureManager.Instance;
			if (textureManager != null)
			{
				var resourcesManager = ResourcesManager.Instance;
				int currentLevel = resourcesManager?.GetToolLevel(_selectedUpgradingTool) ?? 1;
				var texture = textureManager.GetToolTexture(_selectedUpgradingTool, currentLevel);
				_upgradingToolSprite.Texture = texture;
				_upgradingToolSprite.Visible = true;
				GD.Print($"Workbench: Set upgrading tool texture for {_selectedUpgradingTool} level {currentLevel}");
			}

			if (_upgradingProgressBar != null)
			{
				if (_upgradingProgress > 0)
				{
					_upgradingProgressBar.Show();
					UpdateUpgradingProgress();
				}
				else
				{
					_upgradingProgressBar.Hide();
				}
			}
		}
	}

	private void OnHammerUsedForUpgrading(int hammerPower)
	{
		if (_selectedUpgradingTool == ToolType.None) return;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		int currentLevel = resourcesManager.GetToolLevel(_selectedUpgradingTool);
		int nextLevel = currentLevel + 1;

		if (!UpgradeTimeRequired.TryGetValue(nextLevel, out int requiredTime))
		{
			GD.PrintErr($"Workbench: No upgrade time requirement found for level {nextLevel}");
			return;
		}

		_upgradingProgress += hammerPower;
		GD.Print($"Workbench: Upgrading progress: {_upgradingProgress}/{requiredTime}");

		if (_upgradingProgressBar != null && _upgradingProgress > 0)
		{
			_upgradingProgressBar.Show();
		}

		UpdateUpgradingProgress();
		SaveUpgradingState();

		if (_upgradingProgress >= requiredTime)
		{
			CompleteUpgrade();
		}
	}

	private void UpdateUpgradingProgress()
	{
		if (_upgradingProgressBar == null || _selectedUpgradingTool == ToolType.None) return;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		int currentLevel = resourcesManager.GetToolLevel(_selectedUpgradingTool);
		int nextLevel = currentLevel + 1;

		if (!UpgradeTimeRequired.TryGetValue(nextLevel, out int requiredTime)) return;

		if (_upgradingProgress > 0)
		{
			_upgradingProgressBar.Show();
			float progressPercentage = (float)_upgradingProgress / requiredTime;
			_upgradingProgressBar.SetProgress(progressPercentage);
			GD.Print($"Workbench: Progress bar updated to {progressPercentage * 100:F1}% ({_upgradingProgress}/{requiredTime})");
		}
		else
		{
			_upgradingProgressBar.Hide();
		}
	}

	private void CompleteUpgrade()
	{
		if (_selectedUpgradingTool == ToolType.None) return;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		int currentLevel = resourcesManager.GetToolLevel(_selectedUpgradingTool);
		int nextLevel = currentLevel + 1;

		// Consume resources when upgrade is completed
		bool resourcesConsumed = false;
		if (nextLevel == 2) // Stone level
		{
			if (resourcesManager.HasResource(ResourceType.Stone, 20) && resourcesManager.HasResource(ResourceType.Stone2, 20))
			{
				bool stoneConsumed = resourcesManager.RemoveResource(ResourceType.Stone, 20);
				bool stone2Consumed = resourcesManager.RemoveResource(ResourceType.Stone2, 20);
				resourcesConsumed = stoneConsumed && stone2Consumed;
			}
		}
		else if (nextLevel >= 3 && nextLevel <= 10)
		{
			if (UpgradeRequirements.TryGetValue(nextLevel, out var requirements) &&
				LevelToBarType.TryGetValue(nextLevel, out var barType))
			{
				if (resourcesManager.HasResource(ResourceType.WoodenBeam, requirements.beams) &&
					resourcesManager.HasResource(barType, requirements.bars))
				{
					bool beamsConsumed = resourcesManager.RemoveResource(ResourceType.WoodenBeam, requirements.beams);
					bool barsConsumed = resourcesManager.RemoveResource(barType, requirements.bars);
					resourcesConsumed = beamsConsumed && barsConsumed;
				}
			}
		}

		if (!resourcesConsumed)
		{
			GD.Print($"Workbench: Cannot complete upgrade - insufficient resources for {_selectedUpgradingTool} level {nextLevel}!");
			_selectedUpgradingTool = ToolType.None;
			_upgradingProgress = 0;
			UpdateUpgradingToolDisplay();
			SaveUpgradingState();
			return;
		}

		// Upgrade the tool level
		resourcesManager.SetToolLevel(_selectedUpgradingTool, nextLevel);

		GD.Print($"Workbench: Completed upgrading {_selectedUpgradingTool} to level {nextLevel}! (consumed resources)");

		// Reset upgrading progress
		_upgradingProgress = 0;

		// Check if player can afford to upgrade to the next level
		if (nextLevel < 10 && CanAffordToolUpgrade(_selectedUpgradingTool, nextLevel + 1))
		{
			// Keep selected for continuous upgrading
			UpdateUpgradingProgress();
			SaveUpgradingState();
			GD.Print($"Workbench: Ready to upgrade {_selectedUpgradingTool} to level {nextLevel + 1}");
		}
		else
		{
			// Auto-deselect if can't afford next upgrade or at max level
			_selectedUpgradingTool = ToolType.None;
			UpdateUpgradingToolDisplay();
			SaveUpgradingState();
			GD.Print($"Workbench: Auto-deselected {_selectedUpgradingTool} - max level reached or insufficient materials");
		}
	}

	private void SaveUpgradingState()
	{
		if (string.IsNullOrEmpty(_saveId)) return;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		var buildings = resourcesManager.GetBuildings();
		var buildingData = buildings.Find(b => b.Id == _saveId);
		if (buildingData != null)
		{
			buildingData.CurrentHealth = _currentHealth;
			buildingData.SelectedCraftingResource = (int)_selectedUpgradingTool; // Reuse this field
			buildingData.CraftingProgress = _upgradingProgress; // Reuse this field
		}
	}
}
