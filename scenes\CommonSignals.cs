using Godot;
using System;

public partial class CommonSignals : Node
{
	public static CommonSignals Instance { get; private set; }

	[Signal]
	public delegate void UseResourceRequestedEventHandler(ResourceType resourceType);

	[Signal]
	public delegate void ResourceUsedEventHandler(ResourceType resourceType, bool success);

	[Signal]
	public delegate void PickaxeUsedEventHandler(Vector2I tilePosition, int damage);

	[Signal]
	public delegate void HammerUsedEventHandler(Vector2I tilePosition, int repairAmount);

	[Signal]
	public delegate void HoeUsedEventHandler(Vector2I tilePosition);

	[Signal]
	public delegate void WateringCanUsedEventHandler(Vector2I tilePosition);

	[Signal]
	public delegate void SeedUsedEventHandler(Vector2I tilePosition, ResourceType seedBagType);

	[Signal]
	public delegate void SeedAnimationRequestedEventHandler(Vector2 playerPosition, Vector2I tilePosition, ResourceType seedBagType);

	[Signal]
	public delegate void SwordUsedEventHandler(Vector2I tilePosition, Vector2 playerPosition, Vector2 attackDirection);

	// Tool usage signal for energy system
	[Signal]
	public delegate void ToolUsedEventHandler();

	// Movement control signals
	[Signal]
	public delegate void PlayerMovementEnabledEventHandler(bool enabled);

	// XP and level signals
	[Signal]
	public delegate void AddXpEventHandler(int xpAmount);

	// Building signals for tutorial NPC
	[Signal]
	public delegate void AnvilBuiltEventHandler();

	[Signal]
	public delegate void BridgeBuiltEventHandler();

	[Signal]
	public delegate void CampfireBuiltEventHandler();

	// Hunting signals for tutorial NPC
	[Signal]
	public delegate void RabbitHuntedEventHandler();

	// Cooking signals for region unlocking
	[Signal]
	public delegate void RabbitLegCookedEventHandler();

	// Tutorial completion signal
	[Signal]
	public delegate void TutorialCompletedEventHandler();

	// Region unlock signals
	[Signal]
	public delegate void RegionUnlockedEventHandler(int regionId);

	public override void _Ready()
	{
		if (Instance == null)
		{
			Instance = this;
		}
		else
		{
			QueueFree();
		}
	}

	public void EmitUseResourceRequested(ResourceType resourceType)
	{
		EmitSignal(SignalName.UseResourceRequested, (int)resourceType);
	}

	public void EmitResourceUsed(ResourceType resourceType, bool success)
	{
		EmitSignal(SignalName.ResourceUsed, (int)resourceType, success);
	}

	public void EmitPickaxeUsed(Vector2I tilePosition, int damage)
	{
		EmitSignal(SignalName.PickaxeUsed, tilePosition, damage);
	}

	public void EmitHammerUsed(Vector2I tilePosition, int repairAmount)
	{
		EmitSignal(SignalName.HammerUsed, tilePosition, repairAmount);
	}

	public void EmitHoeUsed(Vector2I tilePosition)
	{
		EmitSignal(SignalName.HoeUsed, tilePosition);
	}

	public void EmitWateringCanUsed(Vector2I tilePosition)
	{
		EmitSignal(SignalName.WateringCanUsed, tilePosition);
	}

	public void EmitSeedUsed(Vector2I tilePosition, ResourceType seedBagType)
	{
		EmitSignal(SignalName.SeedUsed, tilePosition, (int)seedBagType);
	}

	public void EmitSeedAnimationRequested(Vector2 playerPosition, Vector2I tilePosition, ResourceType seedBagType)
	{
		EmitSignal(SignalName.SeedAnimationRequested, playerPosition, tilePosition, (int)seedBagType);
	}

	public void EmitSwordUsed(Vector2I tilePosition, Vector2 playerPosition, Vector2 attackDirection)
	{
		EmitSignal(SignalName.SwordUsed, tilePosition, playerPosition, attackDirection);
	}

	public void EmitToolUsed()
	{
		EmitSignal(SignalName.ToolUsed);
	}

	public void EmitPlayerMovementEnabled(bool enabled)
	{
		EmitSignal(SignalName.PlayerMovementEnabled, enabled);
	}

	public void EmitAddXp(int xpAmount)
	{
		EmitSignal(SignalName.AddXp, xpAmount);
	}

	public void EmitAnvilBuilt()
	{
		EmitSignal(SignalName.AnvilBuilt);
	}

	public void EmitBridgeBuilt()
	{
		EmitSignal(SignalName.BridgeBuilt);
	}

	public void EmitCampfireBuilt()
	{
		EmitSignal(SignalName.CampfireBuilt);
	}

	public void EmitRabbitHunted()
	{
		EmitSignal(SignalName.RabbitHunted);
	}

	public void EmitRabbitLegCooked()
	{
		EmitSignal(SignalName.RabbitLegCooked);
	}

	public void EmitTutorialCompleted()
	{
		EmitSignal(SignalName.TutorialCompleted);
	}

	public void EmitRegionUnlocked(int regionId)
	{
		EmitSignal(SignalName.RegionUnlocked, regionId);
	}
}
