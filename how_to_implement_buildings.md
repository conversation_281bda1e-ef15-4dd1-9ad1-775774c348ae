# How to Implement Buildings - Complete Guide

This is a comprehensive step-by-step guide for implementing new buildings in the game. Follow this guide exactly to ensure proper building implementation from start to finish.

## Overview

Buildings are interactive structures that players can place on the map. Every building must have:
- Multi-tile footprint (1x1, 2x1, 1x2, 2x2, etc.)
- Health system with pickaxe damage and hammer healing
- Resource requirements for construction using BuildMenu prices
- Save/load persistence
- Visual feedback during placement
- Menu interaction (R key)
- Proper tile occupation and collision detection

## Building Types and Positioning

### Common Building Sizes
- **1x1**: Single tile (16x16px) - Campfire
- **2x1**: Two tiles wide, one tile high (32x16px) - Anvil  
- **1x2**: One tile wide, two tiles high (16x32px) - Grindstone
- **2x2**: Two tiles wide, two tiles high (32x32px) - Workbench
- **2x2**: Furnaces (sprite is 64px tall but occupies only bottom 2x2 tiles)

### Positioning Rules
1. **Standard buildings**: Position sprite at center of occupied tiles
2. **Tall buildings** (like furnaces): Offset sprite up so bottom aligns with occupied tiles
3. **Tile occupation**: Always mark exactly the tiles the building should block

## Step-by-Step Implementation

### Step 1: Add Building to Enums

Add your building to `ObjectType` enum:
- Find the ObjectType enum
- Add your building (e.g., `YourBuilding = 25`)
- Maintain sequential numbering

### Step 2: Add Building Prices to BuildMenu

In `scenes/UI/build/BuildMenu.cs`, add price constants:
```csharp
// YourBuilding building requirements  
public static readonly int YOURBUILDING_RESOURCE1_REQUIRED = 10;
public static readonly int YOURBUILDING_RESOURCE2_REQUIRED = 5;
```

### Step 3: Create Building Script

Create a new C# script (e.g., `YourBuilding.cs`) with this structure:

```csharp
using Godot;
using System;

public partial class YourBuilding : Node2D, IDestroyableObject
{
    [Export] public ObjectType BuildingType { get; set; } = ObjectType.YourBuilding;
    [Export] public int MaxHealth { get; set; } = 20;

    private const int BUILDING_WIDTH = 2;  // Set your building width
    private const int BUILDING_HEIGHT = 1; // Set your building height  
    private const int TILE_SIZE = 16;

    private int _currentHealth;
    private Vector2I _topLeftTilePosition;
    private CustomDataLayerManager _customDataManager;
    private bool _isPlaced = false;
    private bool _isBeingDestroyed = false;
    private string _saveId = "";

    // Node references - adjust names to match your scene
    private Sprite2D _sprite;
    private ProgressBar _progressBar;
    private Area2D _playerDetector;
    
    // Colors for hit animation
    private readonly Color _normalColor = Colors.White;
    private readonly Color _hitColor = new Color(1.0f, 0.42f, 0.27f); // #ff6c44
}
```

### Step 4: Implement Core Methods

#### _Ready Method
```csharp
public override void _Ready()
{
    _currentHealth = MaxHealth;
    
    // Get node references - adjust paths to match your scene structure
    _sprite = GetNode<Sprite2D>("YourBuildingSprite");
    _progressBar = GetNode<ProgressBar>("ProgressBar");
    _playerDetector = GetNode<Area2D>("PlayerDetector");
    
    // Hide health bar initially
    if (_progressBar != null)
    {
        _progressBar.Hide();
    }
    
    // Connect player detection signals
    if (_playerDetector != null)
    {
        _playerDetector.BodyEntered += OnPlayerEntered;
        _playerDetector.BodyExited += OnPlayerExited;
    }
    
    // Get CustomDataLayerManager
    _customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");
    
    // Connect to damage/repair signals
    if (CommonSignals.Instance != null)
    {
        CommonSignals.Instance.PickaxeUsed += OnPickaxeUsed;
        CommonSignals.Instance.HammerUsed += OnHammerUsed;
    }
}
```

#### Positioning Methods
```csharp
public void SetTilePosition(Vector2I topLeftTile)
{
    _topLeftTilePosition = topLeftTile;
    
    // Standard positioning (center of occupied tiles)
    float centerX = (topLeftTile.X + BUILDING_WIDTH * 0.5f) * TILE_SIZE;
    float centerY = (topLeftTile.Y + BUILDING_HEIGHT * 0.5f) * TILE_SIZE;
    
    // For tall buildings (like furnaces), offset up by 1 tile:
    // float centerY = (topLeftTile.Y + BUILDING_HEIGHT * 0.5f) * TILE_SIZE - TILE_SIZE;
    
    GlobalPosition = new Vector2(centerX, centerY);
}

public Vector2I GetTopLeftTilePosition()
{
    return _topLeftTilePosition;
}
```

### Step 5: Implement Placement System

#### Placement Validation
```csharp
public bool CanBePlacedAt(Vector2I topLeftTile)
{
    if (_customDataManager == null) return false;

    for (int x = 0; x < BUILDING_WIDTH; x++)
    {
        for (int y = 0; y < BUILDING_HEIGHT; y++)
        {
            Vector2I tilePos = topLeftTile + new Vector2I(x, y);
            var tileData = _customDataManager.GetTileData(tilePos);

            if (tileData.ObjectTypePlaced != ObjectTypePlaced.None || tileData.Region <= 0)
            {
                return false;
            }
        }
    }

    return true;
}
```

#### Resource Consumption and Placement
```csharp
public void PlaceBuilding()
{
    var resourcesManager = ResourcesManager.Instance;
    if (resourcesManager == null) return;

    // Check and consume resources using BuildMenu prices
    if (!resourcesManager.HasResource(ResourceType.Resource1, BuildMenu.YOURBUILDING_RESOURCE1_REQUIRED) ||
        !resourcesManager.HasResource(ResourceType.Resource2, BuildMenu.YOURBUILDING_RESOURCE2_REQUIRED))
    {
        GD.Print("YourBuilding: Not enough resources!");
        return;
    }

    if (!resourcesManager.RemoveResource(ResourceType.Resource1, BuildMenu.YOURBUILDING_RESOURCE1_REQUIRED) ||
        !resourcesManager.RemoveResource(ResourceType.Resource2, BuildMenu.YOURBUILDING_RESOURCE2_REQUIRED))
    {
        GD.Print("YourBuilding: Failed to consume resources!");
        return;
    }

    PlaceBuilding(_topLeftTilePosition, _customDataManager);
}

public void PlaceBuilding(Vector2I topLeftTilePosition, CustomDataLayerManager customDataManager)
{
    _topLeftTilePosition = topLeftTilePosition;
    _customDataManager = customDataManager;

    // Mark tiles as occupied
    for (int x = 0; x < BUILDING_WIDTH; x++)
    {
        for (int y = 0; y < BUILDING_HEIGHT; y++)
        {
            Vector2I tilePos = _topLeftTilePosition + new Vector2I(x, y);
            _customDataManager.SetObjectPlaced(tilePos, ObjectTypePlaced.Building);
        }
    }

    SetTilePosition(topLeftTilePosition);
    _isPlaced = true;

    // Set normal color
    if (_sprite != null)
    {
        _sprite.Modulate = _normalColor;
    }

    // Register with save system
    _saveId = ResourcesManager.Instance?.AddBuilding(_topLeftTilePosition, "YourBuilding", (int)BuildingType);

    GD.Print($"YourBuilding: Placed at {_topLeftTilePosition}");
}
```

### Step 6: Implement Health System

#### Health Bar Management
```csharp
private void UpdateHPBar()
{
    if (_progressBar == null) return;

    if (_currentHealth < MaxHealth)
    {
        _progressBar.Value = (double)_currentHealth / MaxHealth * 100;
        _progressBar.Show();
    }
    else
    {
        _progressBar.Hide();
    }
}
```

#### Damage System
```csharp
public void TakeDamage(int damage)
{
    if (_isBeingDestroyed) return;

    _currentHealth -= damage;
    UpdateHPBar();
    SaveBuildingState(); // Save health changes
    PlayHitAnimation();

    if (_currentHealth <= 0)
    {
        DestroyBuilding();
    }
}

private void OnPickaxeUsed(Vector2I tilePosition, int damage)
{
    if (!_isPlaced) return;

    for (int x = 0; x < BUILDING_WIDTH; x++)
    {
        for (int y = 0; y < BUILDING_HEIGHT; y++)
        {
            Vector2I occupiedTile = _topLeftTilePosition + new Vector2I(x, y);
            if (occupiedTile == tilePosition)
            {
                Vector2I playerTile = GetPlayerTilePosition();
                if (CanBeHitFrom(playerTile, tilePosition))
                {
                    TakeDamage(damage);
                }
                return;
            }
        }
    }
}
```

#### Healing System
```csharp
public void Repair(int repairAmount)
{
    if (_isBeingDestroyed) return;

    _currentHealth = Math.Min(_currentHealth + repairAmount, MaxHealth);
    PlayHitAnimation();
    UpdateHPBar();
    SaveBuildingState(); // Save health changes

    GD.Print($"YourBuilding: Repaired for {repairAmount} health. Current: {_currentHealth}/{MaxHealth}");
}

private void OnHammerUsed(Vector2I tilePosition, int repairAmount)
{
    if (!_isPlaced) return;

    for (int x = 0; x < BUILDING_WIDTH; x++)
    {
        for (int y = 0; y < BUILDING_HEIGHT; y++)
        {
            Vector2I occupiedTile = _topLeftTilePosition + new Vector2I(x, y);
            if (occupiedTile == tilePosition)
            {
                Vector2I playerTile = GetPlayerTilePosition();
                if (CanBeHitFrom(playerTile))
                {
                    Repair(repairAmount);
                }
                return;
            }
        }
    }
}
```

### Step 7: Implement Save/Load System

#### Save State Method
```csharp
private void SaveBuildingState()
{
    if (string.IsNullOrEmpty(_saveId)) return;

    var resourcesManager = ResourcesManager.Instance;
    if (resourcesManager == null) return;

    var buildings = resourcesManager.GetBuildings();
    var buildingData = buildings.Find(b => b.Id == _saveId);
    if (buildingData != null)
    {
        buildingData.CurrentHealth = _currentHealth;
    }
}
```

#### Load From Save Method
```csharp
public void LoadFromSave(Vector2I topLeftTilePosition, CustomDataLayerManager customDataManager, int health)
{
    _topLeftTilePosition = topLeftTilePosition;
    _customDataManager = customDataManager;
    _currentHealth = health;

    // Mark tiles as occupied when loading from save
    for (int x = 0; x < BUILDING_WIDTH; x++)
    {
        for (int y = 0; y < BUILDING_HEIGHT; y++)
        {
            Vector2I tilePos = _topLeftTilePosition + new Vector2I(x, y);
            _customDataManager.SetObjectPlaced(tilePos, ObjectTypePlaced.Building);
        }
    }

    // Position building (use same logic as SetTilePosition)
    float centerX = (_topLeftTilePosition.X + BUILDING_WIDTH * 0.5f) * TILE_SIZE;
    float centerY = (_topLeftTilePosition.Y + BUILDING_HEIGHT * 0.5f) * TILE_SIZE;
    // For tall buildings: float centerY = (_topLeftTilePosition.Y + BUILDING_HEIGHT * 0.5f) * TILE_SIZE - TILE_SIZE;
    GlobalPosition = new Vector2(centerX, centerY);

    UpdateHPBar();
    _isPlaced = true;
}
```

### Step 8: Implement Menu System

#### Player Detection
```csharp
private void OnPlayerEntered(Node2D body)
{
    if (body.Name == "Player")
    {
        GD.Print("YourBuilding: Player entered range - press 'R' to open menu");
    }
}

private void OnPlayerExited(Node2D body)
{
    if (body.Name == "Player")
    {
        GD.Print("YourBuilding: Player left range");
        CloseMenu();
    }
}
```

#### Input Handling
```csharp
public override void _Input(InputEvent @event)
{
    if (@event is InputEventKey keyEvent && keyEvent.Pressed)
    {
        if (keyEvent.Keycode == Key.R && _playerDetector != null)
        {
            var bodies = _playerDetector.GetOverlappingBodies();
            foreach (Node2D body in bodies)
            {
                if (body.Name == "Player")
                {
                    OpenMenu();
                    break;
                }
            }
        }
    }
}
```

#### Menu Management
```csharp
private void OpenMenu()
{
    // Set the correct building reference in the menu before opening
    if (MenuManager.Instance != null)
    {
        var buildingMenu = MenuManager.Instance.GetRegisteredMenu("YourBuildingMenu") as YourBuildingMenu;
        if (buildingMenu != null)
        {
            buildingMenu.SetBuilding(this);
        }
        MenuManager.Instance.OpenMenu("YourBuildingMenu");
    }
    else
    {
        // Fallback to direct opening if MenuManager is not available
        GD.PrintErr("YourBuilding: MenuManager not found!");
    }
}

private void CloseMenu()
{
    if (MenuManager.Instance != null)
    {
        MenuManager.Instance.CloseMenu("YourBuildingMenu");
    }
}
```

### Step 9: Implement Utility Methods

#### Hit Detection and Animation
```csharp
private bool CanBeHitFrom(Vector2I playerTile, Vector2I targetTile)
{
    float distance = playerTile.DistanceTo(targetTile);
    return distance <= 2.0f; // Adjust hit range as needed
}

private bool CanBeHitFrom(Vector2I playerTile)
{
    float distance = playerTile.DistanceTo(_topLeftTilePosition);
    return distance <= 2.0f; // Adjust hit range as needed
}

private Vector2I GetPlayerTilePosition()
{
    var player = GetNode<Node2D>("/root/world/Player");
    if (player != null)
    {
        Vector2 playerPos = player.GlobalPosition;
        return new Vector2I((int)(playerPos.X / TILE_SIZE), (int)(playerPos.Y / TILE_SIZE));
    }
    return Vector2I.Zero;
}

private void PlayHitAnimation()
{
    if (_sprite == null) return;

    // Create tween for hit animation
    var tween = CreateTween();
    tween.SetParallel(true);

    // Flash red color
    tween.TweenProperty(_sprite, "modulate", _hitColor, 0.1f);
    tween.TweenProperty(_sprite, "modulate", _normalColor, 0.2f).SetDelay(0.1f);
}
```

### Step 10: Integration Steps

#### Add to BuildingManager
In `scenes/BuildingManager.cs`, add your building to the LoadBuilding method:
```csharp
case "YourBuilding":
    return LoadYourBuilding(buildingData);
```

#### Add to BuildingPlacer
In `scenes/BuildingPlacer.cs`, add placement method and update positioning logic.

#### Add to BuildMenu
In `scenes/UI/build/BuildMenu.cs`, add button, affordability check, and build button handler.

## Summary

Following this guide ensures your building will have:
- ✅ Proper tile occupation and collision detection
- ✅ Health system with pickaxe damage and hammer healing
- ✅ Save/load persistence with health restoration
- ✅ Menu interaction with R key
- ✅ Resource consumption using centralized BuildMenu prices
- ✅ Visual feedback during placement
- ✅ Hit animations and health bar display
- ✅ Proper positioning for different building sizes

This guide covers all aspects needed for a fully functional building that integrates seamlessly with the existing game systems.
