using Godot;
using System;

public partial class Rabbit : CharacterBody2D
{
	public enum RabbitState
	{
		Idle,
		Moving,
		Sleeping,
		Fleeing,
		Searching
	}

	public enum Direction
	{
		Up = 0,
		Down = 1,
		Left = 2,
		Right = 3
	}

	[Export] public float MaxMovementDistance { get; set; } = 80.0f;
	[Export] public float SafeDistance { get; set; } = 64.0f;
	[Export] public float DetectionRange { get; set; } = 48.0f;
	[Export] public float IdleTimeMin { get; set; } = 2.0f;
	[Export] public float IdleTimeMax { get; set; } = 5.0f;
	[Export] public float SleepTimeMin { get; set; } = 8.0f;
	[Export] public float SleepTimeMax { get; set; } = 15.0f;
	[Export] public float MovementSpeed { get; set; } = 30.0f;
	[Export] public float FleeSpeed { get; set; } = 30.0f;
	[Export] public int Health { get; set; } = 3;

	private RabbitState _currentState = RabbitState.Idle;
	private Direction _currentDirection = Direction.Down;
	private Timer _stateTimer;
	private AnimationPlayer _animationPlayer;
	private Sprite2D _sprite;
	private ProgressBar _hpBar;
	private Vector2 _startPosition;
	private Vector2 _targetPosition;
	private float _movementProgress = 0.0f;
	private Vector2I _tilePosition;
	private CustomDataLayerManager _customDataManager;
	private int _currentHealth;
	private bool _wasHitByPlayer = false;
	private Timer _hitMemoryTimer;
	private int _assignedRegion = 1;
	private Timer _damageFlashTimer;
	private bool _isFlashing = false;
	private Tween _knockbackTween;
	private bool _isBeingKnockedBack = false;
	private Area2D _hitArea;

	private readonly Vector2[] _directionVectors = {
		Vector2.Up,
		Vector2.Down,
		Vector2.Left,
		Vector2.Right
	};

	public override void _Ready()
	{
		_currentHealth = Health;
		SetupNodes();
		SetupTimer();
		SetupHitMemoryTimer();
		SetupDamageFlashTimer();

		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.SwordUsed += OnSwordUsed;
		}

		UpdateHPBar();

		ChangeState(RabbitState.Idle);
	}

	private void SetupNodes()
	{
		_sprite = GetNode<Sprite2D>("Sprite2D");
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");

		_hpBar = GetNode<ProgressBar>("ProgressBar");
		if (_hpBar == null)
		{
			GD.PrintErr("Rabbit: ProgressBar node not found!");
		}

		_hitArea = new Area2D();
		_hitArea.Name = "HitArea";
		AddChild(_hitArea);

		var hitShape = new CollisionShape2D();
		var circleShape = new CircleShape2D();
		circleShape.Radius = 8.0f;
		hitShape.Shape = circleShape;
		_hitArea.AddChild(hitShape);


	}





	private void SetupTimer()
	{
		_stateTimer = new Timer();
		_stateTimer.WaitTime = 1.0f;
		_stateTimer.OneShot = true;
		_stateTimer.Timeout += OnStateTimerTimeout;
		AddChild(_stateTimer);
	}

	private void SetupHitMemoryTimer()
	{
		_hitMemoryTimer = new Timer();
		_hitMemoryTimer.WaitTime = 15.0f;
		_hitMemoryTimer.OneShot = true;
		_hitMemoryTimer.Timeout += OnHitMemoryTimeout;
		AddChild(_hitMemoryTimer);
	}

	private void SetupDamageFlashTimer()
	{
		_damageFlashTimer = new Timer();
		_damageFlashTimer.WaitTime = 0.4f;
		_damageFlashTimer.OneShot = true;
		_damageFlashTimer.Timeout += OnDamageFlashTimeout;
		AddChild(_damageFlashTimer);
	}

	private string GetDirectionName(Direction direction)
	{
		return direction switch
		{
			Direction.Up => "up",
			Direction.Down => "down",
			Direction.Left => "left",
			Direction.Right => "right",
			_ => "down"
		};
	}

	private void ChangeState(RabbitState newState)
	{
		_currentState = newState;
		
		switch (newState)
		{
			case RabbitState.Idle:
				StartIdleState();
				break;
			case RabbitState.Moving:
				StartMovingState();
				break;
			case RabbitState.Sleeping:
				StartSleepingState();
				break;
			case RabbitState.Fleeing:
				StartFleeingState();
				break;
			case RabbitState.Searching:
				StartSearchingState();
				break;
		}
	}

	private void StartIdleState()
	{
		float idleTime = (float)GD.RandRange(IdleTimeMin, IdleTimeMax);
		_stateTimer.WaitTime = idleTime;
		_stateTimer.Start();
		
		PlayAnimation($"idle_{GetDirectionName(_currentDirection)}");
	}

	private void StartMovingState()
	{
		ChooseRandomDirection();
		ChooseMovementTarget();
		
		PlayAnimation($"move_{GetDirectionName(_currentDirection)}");
	}

	private void StartSleepingState()
	{
		float sleepTime = (float)GD.RandRange(SleepTimeMin, SleepTimeMax);
		_stateTimer.WaitTime = sleepTime;
		_stateTimer.Start();
		
		PlayAnimation($"sleep_{GetDirectionName(_currentDirection)}");
	}

	private void StartFleeingState()
	{
		ChooseFleeDirection();
		ChooseMovementTarget();
		
		PlayAnimation($"move_{GetDirectionName(_currentDirection)}");
	}

	private void StartSearchingState()
	{
		ChooseRandomDirection();
		ChooseMovementTarget();
		
		PlayAnimation($"move_{GetDirectionName(_currentDirection)}");
	}

	private void PlayAnimation(string animationName)
	{
		if (_animationPlayer != null && _animationPlayer.HasAnimation(animationName))
		{
			_animationPlayer.Play(animationName);
		}
	}

	private void ChooseRandomDirection()
	{
		_currentDirection = (Direction)GD.RandRange(0, 3);
	}

	private void ChooseFleeDirection()
	{
		var player = GetPlayerNode();
		if (player == null)
		{
			ChooseRandomDirection();
			return;
		}

		Vector2 fleeDirection = (GlobalPosition - player.GlobalPosition).Normalized();
		_currentDirection = GetDirectionFromVector(fleeDirection);
	}

	private void ChooseMovementTarget()
	{
		Vector2 direction = _directionVectors[(int)_currentDirection];
		float distance = (float)GD.RandRange(16.0f, MaxMovementDistance);

		_startPosition = GlobalPosition;
		_targetPosition = GlobalPosition + direction * distance;
		_movementProgress = 0.0f;

		if (!IsPathClear(_startPosition, _targetPosition))
		{
			_targetPosition = FindAlternativeTarget();
		}
	}

	private bool IsPathClear(Vector2 from, Vector2 to)
	{
		var spaceState = GetWorld2D().DirectSpaceState;
		var query = PhysicsRayQueryParameters2D.Create(from, to);
		query.CollisionMask = 1;

		var result = spaceState.IntersectRay(query);
		return result.Count == 0;
	}

	private Vector2 FindAlternativeTarget()
	{
		for (int i = 0; i < 8; i++)
		{
			Vector2 testDirection = _directionVectors[i % 4];
			if (i >= 4)
			{
				testDirection = testDirection.Rotated(Mathf.Pi / 4);
			}

			Vector2 testTarget = GlobalPosition + testDirection * 32.0f;

			if (IsPathClear(GlobalPosition, testTarget))
			{
				return testTarget;
			}
		}

		return GlobalPosition;
	}

	private PlayerController GetPlayerNode()
	{
		return GetNode<PlayerController>("/root/world/Player");
	}



	private void OnStateTimerTimeout()
	{
		switch (_currentState)
		{
			case RabbitState.Idle:
				if (GD.Randf() < 0.2f)
				{
					ChangeState(RabbitState.Sleeping);
				}
				else
				{
					ChangeState(RabbitState.Moving);
				}
				break;

			case RabbitState.Sleeping:
				ChangeState(RabbitState.Idle);
				break;
		}
	}

	public override void _PhysicsProcess(double delta)
	{
		HandleMovement(delta);
		CheckPlayerDistance();
		UpdateTilePosition();
	}

	private void HandleMovement(double delta)
	{
		if (_isBeingKnockedBack) return;

		if (_currentState == RabbitState.Moving || _currentState == RabbitState.Fleeing || _currentState == RabbitState.Searching)
		{
			float speed = _currentState == RabbitState.Fleeing ? FleeSpeed : MovementSpeed;

			_movementProgress += (float)(speed * delta / _startPosition.DistanceTo(_targetPosition));
			_movementProgress = Mathf.Clamp(_movementProgress, 0.0f, 1.0f);

			GlobalPosition = _startPosition.Lerp(_targetPosition, _movementProgress);

			// Update animation direction during movement if needed
			UpdateMovementAnimation();

			if (_movementProgress >= 1.0f)
			{
				if (_currentState == RabbitState.Fleeing && _wasHitByPlayer)
				{
					StartFleeingState();
				}
				else
				{
					ChangeState(RabbitState.Idle);
				}
			}
		}
	}

	private void UpdateMovementAnimation()
	{
		if (_currentState != RabbitState.Moving && _currentState != RabbitState.Fleeing && _currentState != RabbitState.Searching)
			return;

		// Calculate current movement direction based on actual movement
		Vector2 movementDirection = (_targetPosition - _startPosition).Normalized();
		Direction newDirection = GetDirectionFromVector(movementDirection);

		// Only update animation if direction actually changed
		if (newDirection != _currentDirection)
		{
			_currentDirection = newDirection;
			PlayAnimation($"move_{GetDirectionName(_currentDirection)}");
		}
	}

	private Direction GetDirectionFromVector(Vector2 direction)
	{
		if (Math.Abs(direction.X) > Math.Abs(direction.Y))
		{
			return direction.X > 0 ? Direction.Right : Direction.Left;
		}
		else
		{
			return direction.Y > 0 ? Direction.Down : Direction.Up;
		}
	}

	private void CheckPlayerDistance()
	{
		if (!_wasHitByPlayer) return;

		var player = GetPlayerNode();
		if (player == null) return;

		float distanceToPlayer = GlobalPosition.DistanceTo(player.GlobalPosition);

		if (distanceToPlayer <= DetectionRange)
		{
			if (_currentState != RabbitState.Fleeing)
			{
				ChangeState(RabbitState.Fleeing);
			}
		}
		else if (distanceToPlayer > SafeDistance && _currentState == RabbitState.Fleeing)
		{
			ChangeState(RabbitState.Idle);
		}
	}

	private bool IsPlayerTooClose()
	{
		var player = GetPlayerNode();
		if (player == null) return false;

		return GlobalPosition.DistanceTo(player.GlobalPosition) < SafeDistance;
	}

	private void UpdateTilePosition()
	{
		Vector2I newTilePosition = new Vector2I(
			Mathf.FloorToInt(GlobalPosition.X / 16),
			Mathf.FloorToInt(GlobalPosition.Y / 16)
		);

		if (newTilePosition != _tilePosition)
		{
			if (_customDataManager != null)
			{
				_customDataManager.ClearObjectPlaced(_tilePosition);
				_customDataManager.SetObjectPlaced(newTilePosition, ObjectTypePlaced.Animal);
			}
			_tilePosition = newTilePosition;
		}
	}

	public void TakeDamage(int damage)
	{
		_currentHealth -= damage;

		UpdateHPBar();

		_wasHitByPlayer = true;
		_hitMemoryTimer.Start();

		if (_currentHealth <= 0)
		{
			Die();
		}
		else
		{
			ChangeState(RabbitState.Fleeing);
		}
	}

	private void OnHitMemoryTimeout()
	{
		_wasHitByPlayer = false;

		if (_currentState == RabbitState.Fleeing)
		{
			ChangeState(RabbitState.Idle);
		}
	}

	private void Die()
	{
		DropResources();
		CommonSignals.Instance?.EmitAddXp(8);
		CommonSignals.Instance?.EmitRabbitHunted();

		if (_customDataManager != null)
		{
			_customDataManager.ClearObjectPlaced(_tilePosition);
		}

		QueueFree();
	}

	private void DropResources()
	{
		// Drop 2 raw rabbit legs
		DroppedResource.SpawnResource(GlobalPosition, ResourceType.RawRabbitLeg, 2);
	}

	public Vector2I GetTilePosition()
	{
		return _tilePosition;
	}

	public void SetTilePosition(Vector2I position)
	{
		_tilePosition = position;
		GlobalPosition = new Vector2(position.X * 16 + 8, position.Y * 16 + 8);

		if (_customDataManager != null)
		{
			_customDataManager.SetObjectPlaced(_tilePosition, ObjectTypePlaced.Animal);
		}
	}

	public void SetRegion(int regionId)
	{
		_assignedRegion = regionId;
	}

	public int GetRegion()
	{
		return _assignedRegion;
	}

	public RabbitSaveData GetSaveData()
	{
		return new RabbitSaveData
		{
			Position = GlobalPosition,
			Health = _currentHealth,
			AssignedRegion = _assignedRegion,
			WasHitByPlayer = _wasHitByPlayer,
			CurrentState = _currentState,
			CurrentDirection = _currentDirection
		};
	}

	public void LoadFromSaveData(RabbitSaveData data)
	{
		GlobalPosition = data.Position;
		_currentHealth = data.Health;
		_assignedRegion = data.AssignedRegion;
		_wasHitByPlayer = data.WasHitByPlayer;
		_currentDirection = data.CurrentDirection;

		_tilePosition = new Vector2I(
			Mathf.FloorToInt(GlobalPosition.X / 16),
			Mathf.FloorToInt(GlobalPosition.Y / 16)
		);

		CallDeferred(nameof(InitializeStateAfterReady));

		if (_wasHitByPlayer && _hitMemoryTimer != null)
		{
			_hitMemoryTimer.Start();
		}
	}

	private void InitializeStateAfterReady()
	{
		// Only change state if animation player is ready
		if (_animationPlayer != null)
		{
			ChangeState(_currentState);
		}
	}

	private void OnSwordUsed(Vector2I tilePosition, Vector2 playerPosition, Vector2 attackDirection)
	{
		float distance = GlobalPosition.DistanceTo(playerPosition);
		GD.Print($"Rabbit at {GlobalPosition}, Player at {playerPosition}, Distance: {distance}");

		if (IsInAttackArc(playerPosition, attackDirection))
		{
			GD.Print("Rabbit hit by sword!");
			ApplyKnockback(playerPosition);
			ApplyDamageFlash();
			TakeDamage(2);
		}
		else
		{
			GD.Print("Rabbit not in attack arc");
		}
	}



	private bool IsInAttackArc(Vector2 playerPosition, Vector2 attackDirection)
	{
		const float AttackRadius = 32.0f;

		Vector2 toRabbit = GlobalPosition - playerPosition;
		float distance = toRabbit.Length();

		if (distance > AttackRadius)
			return false;

		Vector2 toRabbitNormalized = toRabbit.Normalized();
		float dotProduct = attackDirection.Dot(toRabbitNormalized);

		return dotProduct >= 0;
	}

	public void ApplyKnockback(Vector2 playerPosition)
	{
		if (_isBeingKnockedBack) return;

		const float KnockbackForce = 24.0f;
		const float KnockbackDuration = 0.2f;

		Vector2 knockbackDirection = (GlobalPosition - playerPosition).Normalized();
		Vector2 startPosition = GlobalPosition;
		Vector2 targetPosition = GlobalPosition + knockbackDirection * KnockbackForce;

		// Check if the knockback path is clear
		if (!IsPathClear(startPosition, targetPosition))
		{
			// Find the maximum safe distance we can move
			targetPosition = FindSafeKnockbackPosition(startPosition, knockbackDirection, KnockbackForce);
		}

		_isBeingKnockedBack = true;

		_startPosition = GlobalPosition;
		_targetPosition = GlobalPosition;
		_movementProgress = 0.0f;

		_knockbackTween?.Kill();
		_knockbackTween = CreateTween();

		_knockbackTween.TweenProperty(this, "global_position", targetPosition, KnockbackDuration);
		_knockbackTween.SetEase(Tween.EaseType.Out);
		_knockbackTween.SetTrans(Tween.TransitionType.Quart);

		_knockbackTween.TweenCallback(Callable.From(() => {
			_isBeingKnockedBack = false;
			UpdateTilePosition();

			_startPosition = GlobalPosition;
			_targetPosition = GlobalPosition;
			_movementProgress = 0.0f;
		}));
	}

	private Vector2 FindSafeKnockbackPosition(Vector2 startPosition, Vector2 direction, float maxDistance)
	{
		const float StepSize = 2.0f;
		Vector2 currentPosition = startPosition;

		for (float distance = StepSize; distance <= maxDistance; distance += StepSize)
		{
			Vector2 testPosition = startPosition + direction * distance;

			if (!IsPathClear(currentPosition, testPosition))
			{
				// Return the last safe position
				return currentPosition;
			}

			currentPosition = testPosition;
		}

		// If we made it through the entire distance, return the full knockback position
		return startPosition + direction * maxDistance;
	}

	public void ApplyDamageFlash()
	{
		if (_isFlashing) return;

		_isFlashing = true;

		_sprite.Modulate = new Color(1.5f, 0.6f, 0.6f, 1.0f);

		_damageFlashTimer.Start();
	}

	private void OnDamageFlashTimeout()
	{
		_isFlashing = false;

		_sprite.Modulate = new Color(1.0f, 1.0f, 1.0f, 1.0f);
	}

	private void UpdateHPBar()
	{
		if (_hpBar == null) return;

		float healthPercentage = (float)_currentHealth / Health;

		if (_currentHealth >= Health)
		{
			_hpBar.Hide();
		}
		else
		{
			_hpBar.Show();
			_hpBar.SetProgress(healthPercentage);
		}
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.SwordUsed -= OnSwordUsed;
		}



		if (_customDataManager != null)
		{
			_customDataManager.ClearObjectPlaced(_tilePosition);
		}
	}
}
